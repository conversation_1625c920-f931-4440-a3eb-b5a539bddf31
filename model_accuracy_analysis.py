"""
Comprehensive Model Accuracy Analysis
Shows multiple accuracy metrics for all three models
"""

import numpy as np
import pandas as pd
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score, mean_absolute_percentage_error
import joblib

# Load data
X_test = np.load('X_test.npy')
y_test = np.load('y_test.npy')

# Load models
lgb_model = joblib.load('clean_tuned_lightgbm.pkl')
gb_model = joblib.load('clean_tuned_gradient_boosting.pkl')
ridge_model = joblib.load('clean_tuned_ridge_regression.pkl')
scaler = joblib.load('clean_tuned_scaler.pkl')

# Scale data for Ridge
X_test_scaled = scaler.transform(X_test)

def calculate_accuracy_metrics(y_true, y_pred, model_name):
    """Calculate comprehensive accuracy metrics"""
    
    # Basic metrics
    mae = mean_absolute_error(y_true, y_pred)
    rmse = np.sqrt(mean_squared_error(y_true, y_pred))
    r2 = r2_score(y_true, y_pred)
    mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
    
    # Accuracy metrics (percentage-based)
    # 1. R² as percentage (coefficient of determination)
    r2_percentage = r2 * 100
    
    # 2. Accuracy within tolerance bands
    tolerance_5 = np.mean(np.abs(y_pred - y_true) / y_true <= 0.05) * 100  # Within 5%
    tolerance_10 = np.mean(np.abs(y_pred - y_true) / y_true <= 0.10) * 100  # Within 10%
    tolerance_15 = np.mean(np.abs(y_pred - y_true) / y_true <= 0.15) * 100  # Within 15%
    tolerance_20 = np.mean(np.abs(y_pred - y_true) / y_true <= 0.20) * 100  # Within 20%
    
    # 3. Mean Accuracy (100% - MAPE)
    mean_accuracy = 100 - mape
    
    # 4. Prediction accuracy (how close predictions are on average)
    prediction_accuracy = 100 - (mae / np.mean(y_true)) * 100
    
    return {
        'Model': model_name,
        'MAE': mae,
        'RMSE': rmse,
        'MAPE': mape,
        'R²': r2,
        'R²_Percentage': r2_percentage,
        'Mean_Accuracy': mean_accuracy,
        'Prediction_Accuracy': prediction_accuracy,
        'Accuracy_Within_5%': tolerance_5,
        'Accuracy_Within_10%': tolerance_10,
        'Accuracy_Within_15%': tolerance_15,
        'Accuracy_Within_20%': tolerance_20
    }

print("COMPREHENSIVE MODEL ACCURACY ANALYSIS")
print("="*60)

# Get predictions
lgb_pred = lgb_model.predict(X_test)
gb_pred = gb_model.predict(X_test)
ridge_pred = ridge_model.predict(X_test_scaled)

# Calculate metrics for all models
lgb_metrics = calculate_accuracy_metrics(y_test, lgb_pred, 'LightGBM')
gb_metrics = calculate_accuracy_metrics(y_test, gb_pred, 'Gradient Boosting')
ridge_metrics = calculate_accuracy_metrics(y_test, ridge_pred, 'Ridge Regression')

# Create comprehensive results DataFrame
results_df = pd.DataFrame([lgb_metrics, gb_metrics, ridge_metrics])

print("\n1. BASIC PERFORMANCE METRICS")
print("-" * 40)
basic_metrics = results_df[['Model', 'MAE', 'RMSE', 'MAPE', 'R²']].round(3)
print(basic_metrics.to_string(index=False))

print("\n2. ACCURACY METRICS (PERCENTAGE)")
print("-" * 40)
accuracy_metrics = results_df[['Model', 'R²_Percentage', 'Mean_Accuracy', 'Prediction_Accuracy']].round(2)
print(accuracy_metrics.to_string(index=False))

print("\n3. TOLERANCE-BASED ACCURACY")
print("-" * 40)
tolerance_metrics = results_df[['Model', 'Accuracy_Within_5%', 'Accuracy_Within_10%', 
                               'Accuracy_Within_15%', 'Accuracy_Within_20%']].round(1)
print(tolerance_metrics.to_string(index=False))

print("\n4. DETAILED ACCURACY BREAKDOWN")
print("-" * 40)

for _, row in results_df.iterrows():
    print(f"\n{row['Model']}:")
    print(f"  📊 R² Score: {row['R²']:.3f} ({row['R²_Percentage']:.1f}% of variance explained)")
    print(f"  🎯 Mean Accuracy: {row['Mean_Accuracy']:.1f}% (100% - MAPE)")
    print(f"  📈 Prediction Accuracy: {row['Prediction_Accuracy']:.1f}% (relative to mean)")
    print(f"  ✅ Within 5% tolerance: {row['Accuracy_Within_5%']:.1f}% of predictions")
    print(f"  ✅ Within 10% tolerance: {row['Accuracy_Within_10%']:.1f}% of predictions")
    print(f"  ✅ Within 15% tolerance: {row['Accuracy_Within_15%']:.1f}% of predictions")
    print(f"  ✅ Within 20% tolerance: {row['Accuracy_Within_20%']:.1f}% of predictions")

print("\n5. ACCURACY RANKING")
print("-" * 40)

# Rank models by different accuracy metrics
rankings = pd.DataFrame({
    'Model': results_df['Model'],
    'R²_Rank': results_df['R²'].rank(ascending=False),
    'Mean_Accuracy_Rank': results_df['Mean_Accuracy'].rank(ascending=False),
    'Within_10%_Rank': results_df['Accuracy_Within_10%'].rank(ascending=False),
    'Overall_Rank': (results_df['R²'].rank(ascending=False) + 
                    results_df['Mean_Accuracy'].rank(ascending=False) + 
                    results_df['Accuracy_Within_10%'].rank(ascending=False)) / 3
})

rankings = rankings.sort_values('Overall_Rank')
print(rankings.round(1).to_string(index=False))

print("\n6. ACCURACY INTERPRETATION")
print("-" * 40)

best_model = results_df.loc[results_df['R²'].idxmax()]
print(f"🏆 BEST MODEL: {best_model['Model']}")
print(f"   - Explains {best_model['R²_Percentage']:.1f}% of variance in the data")
print(f"   - Achieves {best_model['Mean_Accuracy']:.1f}% mean accuracy")
print(f"   - {best_model['Accuracy_Within_10%']:.1f}% of predictions within 10% of actual values")

print(f"\n📊 ACCURACY STANDARDS:")
print(f"   - R² > 0.90 (90%): Excellent model")
print(f"   - R² > 0.80 (80%): Good model") 
print(f"   - R² > 0.70 (70%): Acceptable model")
print(f"   - Within 10% tolerance > 80%: High precision")

print(f"\n✅ MODEL ASSESSMENT:")
for _, row in results_df.iterrows():
    r2_status = "Excellent" if row['R²'] > 0.90 else "Good" if row['R²'] > 0.80 else "Acceptable"
    precision_status = "High" if row['Accuracy_Within_10%'] > 80 else "Moderate" if row['Accuracy_Within_10%'] > 60 else "Low"
    print(f"   {row['Model']}: {r2_status} accuracy, {precision_status} precision")

# Save results
results_df.round(3).to_csv('comprehensive_accuracy_analysis.csv', index=False)

print(f"\n📁 Results saved to: comprehensive_accuracy_analysis.csv")
print(f"✅ Comprehensive accuracy analysis completed!")
