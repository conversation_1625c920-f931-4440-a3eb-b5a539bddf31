"""
Complete Model Pipeline: Baseline → Learning Curves → Evaluation → Tuning → Final Evaluation
FINAL VERSION: Strong regularization to ensure gaps < 5
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.ensemble import GradientBoostingRegressor
from sklearn.linear_model import Ridge
from sklearn.model_selection import learning_curve, RandomizedSearchCV
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler
import lightgbm as lgb
import joblib
import warnings
warnings.filterwarnings('ignore')

# Load data
X_train = np.load('X_train.npy')
X_val = np.load('X_val.npy')
X_test = np.load('X_test.npy')
y_train = np.load('y_train.npy')
y_val = np.load('y_val.npy')
y_test = np.load('y_test.npy')

# Scale data for Ridge Regression
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_val_scaled = scaler.transform(X_val)
X_test_scaled = scaler.transform(X_test)

def evaluate_model(model, X_test, y_test):
    pred = model.predict(X_test)
    mae = mean_absolute_error(y_test, pred)
    rmse = np.sqrt(mean_squared_error(y_test, pred))
    r2 = r2_score(y_test, pred)
    mape = np.mean(np.abs((y_test - pred) / y_test)) * 100
    return {'MAE': mae, 'RMSE': rmse, 'R²': r2, 'MAPE': mape}

def plot_learning_curve(model, X, y, model_name):
    train_sizes = np.linspace(0.1, 1.0, 10)
    train_sizes, train_scores, val_scores = learning_curve(
        model, X, y, train_sizes=train_sizes, cv=3, 
        scoring='neg_mean_absolute_error', n_jobs=-1
    )
    
    train_mean = -train_scores.mean(axis=1)
    val_mean = -val_scores.mean(axis=1)
    
    plt.figure(figsize=(10, 6))
    plt.plot(train_sizes, train_mean, 'o-', color='blue', label='Training')
    plt.plot(train_sizes, val_mean, 'o-', color='red', label='Validation')
    plt.xlabel('Training Set Size')
    plt.ylabel('Mean Absolute Error')
    plt.title(f'{model_name} Learning Curve')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(f'{model_name.lower().replace(" ", "_")}_learning_curve.png', dpi=300)
    plt.close()
    
    gap = val_mean[-1] - train_mean[-1]
    return gap

print("COMPLETE MODEL PIPELINE - FINAL VERSION")
print("="*50)

# 1. BASELINE MODELS (Regularized)
print("\n1. BASELINE MODELS")
print("-" * 30)

# LightGBM Baseline (Regularized)
lgb_base = lgb.LGBMRegressor(
    n_estimators=100,
    max_depth=4,
    learning_rate=0.05,
    subsample=0.8,
    colsample_bytree=0.8,
    min_child_samples=20,
    reg_alpha=0.5,
    reg_lambda=0.5,
    random_state=42,
    verbose=-1
)
lgb_base.fit(X_train, y_train)
lgb_base_results = evaluate_model(lgb_base, X_val, y_val)
lgb_base_gap = plot_learning_curve(lgb_base, X_train, y_train, "LightGBM Baseline")
print(f"LightGBM Baseline: MAPE={lgb_base_results['MAPE']:.2f}%, Gap={lgb_base_gap:.2f}")

# Gradient Boosting Baseline (Regularized)
gb_base = GradientBoostingRegressor(
    n_estimators=100,
    max_depth=3,
    learning_rate=0.05,
    subsample=0.8,
    min_samples_split=20,
    min_samples_leaf=10,
    random_state=42
)
gb_base.fit(X_train, y_train)
gb_base_results = evaluate_model(gb_base, X_val, y_val)
gb_base_gap = plot_learning_curve(gb_base, X_train, y_train, "Gradient Boosting Baseline")
print(f"Gradient Boosting Baseline: MAPE={gb_base_results['MAPE']:.2f}%, Gap={gb_base_gap:.2f}")

# Ridge Regression Baseline
ridge_base = Ridge(alpha=10.0, random_state=42)
ridge_base.fit(X_train_scaled, y_train)
ridge_base_results = evaluate_model(ridge_base, X_val_scaled, y_val)
ridge_base_gap = plot_learning_curve(ridge_base, X_train_scaled, y_train, "Ridge Baseline")
print(f"Ridge Baseline: MAPE={ridge_base_results['MAPE']:.2f}%, Gap={ridge_base_gap:.2f}")

# 2. BASELINE EVALUATION
print("\n2. BASELINE EVALUATION")
print("-" * 30)

baseline_results = pd.DataFrame([
    {'Model': 'LightGBM', **lgb_base_results, 'Gap': lgb_base_gap},
    {'Model': 'Gradient Boosting', **gb_base_results, 'Gap': gb_base_gap},
    {'Model': 'Ridge Regression', **ridge_base_results, 'Gap': ridge_base_gap}
])

print(baseline_results.round(2))
baseline_results.to_csv('baseline_results_final.csv', index=False)

# 3. HYPERPARAMETER TUNING (HEAVILY REGULARIZED)
print("\n3. HYPERPARAMETER TUNING")
print("-" * 30)

# LightGBM Tuning (Heavily regularized)
lgb_params = {
    'n_estimators': [50, 80, 100],
    'max_depth': [3, 4],
    'learning_rate': [0.01, 0.03, 0.05],
    'subsample': [0.6, 0.7, 0.8],
    'colsample_bytree': [0.6, 0.7, 0.8],
    'reg_alpha': [0.5, 1.0, 2.0],
    'reg_lambda': [0.5, 1.0, 2.0],
    'min_child_samples': [20, 30, 40]
}

lgb_search = RandomizedSearchCV(
    lgb.LGBMRegressor(random_state=42, verbose=-1),
    lgb_params, n_iter=25, cv=5, scoring='neg_mean_absolute_error',
    random_state=42, n_jobs=-1
)
lgb_search.fit(X_train, y_train)
lgb_tuned = lgb_search.best_estimator_
print(f"LightGBM Best Params: {lgb_search.best_params_}")

# Gradient Boosting Tuning (Heavily regularized)
gb_params = {
    'n_estimators': [50, 80, 100],
    'max_depth': [2, 3],
    'learning_rate': [0.01, 0.03, 0.05],
    'subsample': [0.6, 0.7, 0.8],
    'min_samples_split': [30, 40, 50],
    'min_samples_leaf': [15, 20, 25]
}

gb_search = RandomizedSearchCV(
    GradientBoostingRegressor(random_state=42),
    gb_params, n_iter=25, cv=5, scoring='neg_mean_absolute_error',
    random_state=42, n_jobs=-1
)
gb_search.fit(X_train, y_train)
gb_tuned = gb_search.best_estimator_
print(f"Gradient Boosting Best Params: {gb_search.best_params_}")

# Ridge Regression Tuning
ridge_params = {
    'alpha': [1.0, 10.0, 50.0, 100.0]
}

ridge_search = RandomizedSearchCV(
    Ridge(random_state=42),
    ridge_params, n_iter=4, cv=5, scoring='neg_mean_absolute_error',
    random_state=42, n_jobs=-1
)
ridge_search.fit(X_train_scaled, y_train)
ridge_tuned = ridge_search.best_estimator_
print(f"Ridge Best Params: {ridge_search.best_params_}")

# 4. TUNED MODEL LEARNING CURVES
print("\n4. TUNED MODEL LEARNING CURVES")
print("-" * 30)

lgb_tuned_gap = plot_learning_curve(lgb_tuned, X_train, y_train, "LightGBM Tuned")
gb_tuned_gap = plot_learning_curve(gb_tuned, X_train, y_train, "Gradient Boosting Tuned")
ridge_tuned_gap = plot_learning_curve(ridge_tuned, X_train_scaled, y_train, "Ridge Tuned")

print(f"LightGBM Tuned Gap: {lgb_tuned_gap:.2f}")
print(f"Gradient Boosting Tuned Gap: {gb_tuned_gap:.2f}")
print(f"Ridge Tuned Gap: {ridge_tuned_gap:.2f}")

# 5. TUNED MODEL EVALUATION
print("\n5. TUNED MODEL EVALUATION")
print("-" * 30)

lgb_tuned_results = evaluate_model(lgb_tuned, X_val, y_val)
gb_tuned_results = evaluate_model(gb_tuned, X_val, y_val)
ridge_tuned_results = evaluate_model(ridge_tuned, X_val_scaled, y_val)

print(f"LightGBM Tuned: MAPE={lgb_tuned_results['MAPE']:.2f}%, Gap={lgb_tuned_gap:.2f}")
print(f"Gradient Boosting Tuned: MAPE={gb_tuned_results['MAPE']:.2f}%, Gap={gb_tuned_gap:.2f}")
print(f"Ridge Tuned: MAPE={ridge_tuned_results['MAPE']:.2f}%, Gap={ridge_tuned_gap:.2f}")

tuned_results = pd.DataFrame([
    {'Model': 'LightGBM', **lgb_tuned_results, 'Gap': lgb_tuned_gap},
    {'Model': 'Gradient Boosting', **gb_tuned_results, 'Gap': gb_tuned_gap},
    {'Model': 'Ridge Regression', **ridge_tuned_results, 'Gap': ridge_tuned_gap}
])

print("\nTuned Results:")
print(tuned_results.round(2))
tuned_results.to_csv('tuned_results_final.csv', index=False)

# 6. FINAL TEST EVALUATION
print("\n6. FINAL TEST EVALUATION")
print("-" * 30)

lgb_test_results = evaluate_model(lgb_tuned, X_test, y_test)
gb_test_results = evaluate_model(gb_tuned, X_test, y_test)
ridge_test_results = evaluate_model(ridge_tuned, X_test_scaled, y_test)

print(f"LightGBM Test: MAPE={lgb_test_results['MAPE']:.2f}%")
print(f"Gradient Boosting Test: MAPE={gb_test_results['MAPE']:.2f}%")
print(f"Ridge Test: MAPE={ridge_test_results['MAPE']:.2f}%")

final_results = pd.DataFrame([
    {'Model': 'LightGBM', 'Validation_MAPE': lgb_tuned_results['MAPE'], 'Test_MAPE': lgb_test_results['MAPE'], 'Gap': lgb_tuned_gap},
    {'Model': 'Gradient Boosting', 'Validation_MAPE': gb_tuned_results['MAPE'], 'Test_MAPE': gb_test_results['MAPE'], 'Gap': gb_tuned_gap},
    {'Model': 'Ridge Regression', 'Validation_MAPE': ridge_tuned_results['MAPE'], 'Test_MAPE': ridge_test_results['MAPE'], 'Gap': ridge_tuned_gap}
])

print("\nFinal Comparison:")
print(final_results.round(2))
final_results.to_csv('final_comparison_clean.csv', index=False)

# 7. SAVE MODELS
print("\n7. SAVING MODELS")
print("-" * 30)

joblib.dump(lgb_tuned, 'clean_tuned_lightgbm.pkl')
joblib.dump(gb_tuned, 'clean_tuned_gradient_boosting.pkl')
joblib.dump(ridge_tuned, 'clean_tuned_ridge_regression.pkl')
joblib.dump(scaler, 'clean_tuned_scaler.pkl')

print("Models saved successfully")

# 8. SUMMARY
print("\n8. SUMMARY")
print("-" * 30)

best_model = final_results.loc[final_results['Test_MAPE'].idxmin()]
print(f"Best Model: {best_model['Model']}")
print(f"Test MAPE: {best_model['Test_MAPE']:.2f}%")
print(f"Overfitting Gap: {best_model['Gap']:.2f}")

# Check if all gaps are acceptable
all_gaps = [lgb_tuned_gap, gb_tuned_gap, ridge_tuned_gap]
gaps_ok = all([gap < 5.0 for gap in all_gaps])

print(f"\nGAP ANALYSIS:")
print(f"All gaps < 5.0: {'✅ YES' if gaps_ok else '❌ NO'}")
for model, gap in zip(['LightGBM', 'Gradient Boosting', 'Ridge'], all_gaps):
    status = '✅' if gap < 5.0 else '❌'
    print(f"  {model}: {gap:.2f} {status}")

print("\nPipeline completed successfully!")
print("Files generated:")
print("- baseline_results_final.csv")
print("- tuned_results_final.csv") 
print("- final_comparison_clean.csv")
print("- Learning curve plots for all models")
print("- Clean tuned model files")
